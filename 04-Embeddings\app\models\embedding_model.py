import asyncio
import logging
from typing import List, Optional
import torch
import numpy as np
from sentence_transformers import SentenceTransformer
from concurrent.futures import ThreadPoolExecutor
import os

from ..config import settings

logger = logging.getLogger(__name__)


class EmbeddingModel:
    """BGE-M3 Embedding Model wrapper"""
    
    def __init__(self, model_name: str = None):
        self.model_name = model_name or settings.MODEL_NAME
        self.model = None
        self.device = self._get_device()
        self.executor = ThreadPoolExecutor(max_workers=2)
        self._loaded = False
        
    def _get_device(self) -> str:
        """Determine the best device to use"""
        if settings.DEVICE == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        return settings.DEVICE
    
    async def load_model(self):
        """Load the BGE-M3 model asynchronously"""
        if self._loaded:
            return
            
        logger.info(f"Loading model {self.model_name} on device {self.device}")
        
        try:
            # Run model loading in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                self.executor,
                self._load_model_sync
            )
            self._loaded = True
            logger.info(f"Model {self.model_name} loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise e
    
    def _load_model_sync(self) -> SentenceTransformer:
        """Synchronous model loading with fallback options"""
        # Create cache directory if it doesn't exist
        if settings.MODEL_CACHE_DIR:
            os.makedirs(settings.MODEL_CACHE_DIR, exist_ok=True)

        # Try to load the specified model
        try:
            logger.info(f"Attempting to load model: {self.model_name}")
            model = SentenceTransformer(
                self.model_name,
                device=self.device,
                cache_folder=settings.MODEL_CACHE_DIR
            )
            logger.info(f"Successfully loaded model: {self.model_name}")
        except Exception as e:
            logger.warning(f"Failed to load {self.model_name}: {e}")

            # Fallback models in order of preference
            fallback_models = [
                "sentence-transformers/all-MiniLM-L6-v2",  # Smaller, faster
                "sentence-transformers/paraphrase-MiniLM-L6-v2",  # Lightweight
                "sentence-transformers/all-mpnet-base-v2",  # Good quality
                "distilbert-base-uncased"  # Very basic fallback
            ]

            model = None
            for fallback_model in fallback_models:
                try:
                    logger.info(f"Trying fallback model: {fallback_model}")
                    model = SentenceTransformer(
                        fallback_model,
                        device=self.device,
                        cache_folder=settings.MODEL_CACHE_DIR
                    )
                    logger.info(f"Successfully loaded fallback model: {fallback_model}")
                    self.model_name = fallback_model  # Update model name
                    break
                except Exception as fallback_error:
                    logger.warning(f"Fallback model {fallback_model} also failed: {fallback_error}")
                    continue

            if model is None:
                raise RuntimeError("Failed to load any embedding model")

        # Set model to evaluation mode
        model.eval()

        return model
    
    def is_loaded(self) -> bool:
        """Check if model is loaded"""
        return self._loaded and self.model is not None
    
    async def embed_texts(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        """Generate embeddings for a list of texts"""
        if not self.is_loaded():
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        if not texts:
            return []
        
        # Validate text lengths
        for i, text in enumerate(texts):
            if len(text) > settings.MAX_TEXT_LENGTH:
                logger.warning(f"Text {i} exceeds max length, truncating...")
                texts[i] = text[:settings.MAX_TEXT_LENGTH]
        
        try:
            # Run embedding in thread pool
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                self.executor,
                self._embed_texts_sync,
                texts,
                normalize
            )
            
            return embeddings.tolist()
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise e
    
    def _embed_texts_sync(self, texts: List[str], normalize: bool) -> np.ndarray:
        """Synchronous embedding generation"""
        with torch.no_grad():
            embeddings = self.model.encode(
                texts,
                batch_size=settings.BATCH_SIZE,
                normalize_embeddings=normalize,
                convert_to_numpy=True,
                show_progress_bar=False
            )
        return embeddings
    
    async def embed_single_text(self, text: str, normalize: bool = True) -> List[float]:
        """Generate embedding for a single text"""
        embeddings = await self.embed_texts([text], normalize)
        return embeddings[0] if embeddings else []
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model"""
        if not self.is_loaded():
            return {"status": "not_loaded"}
        
        return {
            "model_name": self.model_name,
            "device": self.device,
            "max_seq_length": getattr(self.model, 'max_seq_length', 'unknown'),
            "embedding_dimension": self.model.get_sentence_embedding_dimension(),
            "status": "loaded"
        }
    
    def __del__(self):
        """Cleanup resources"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
