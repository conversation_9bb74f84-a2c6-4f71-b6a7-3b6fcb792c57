{"name": "ts-api-utils", "version": "1.4.3", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "repository": {"type": "git", "url": "https://github.com/JoshuaKGoldberg/ts-api-utils"}, "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "main": "./lib/index.js", "files": ["lib/", "package.json", "LICENSE.md", "README.md"], "scripts": {"build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "docs": "typedoc", "docs:serve": "npx --yes http-server docs/generated", "format": "prettier \"**/*\" --ignore-unknown", "lint": "eslint . .*js --max-warnings 0", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "lint:knip:production": "knip --production", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "prepare": "husky", "should-semantic-release": "should-semantic-release --verbose", "test": "vitest", "tsc": "tsc"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "devDependencies": {"@phenomnomnominal/tsquery": "^6.1.3", "@release-it/conventional-changelog": "^9.0.0", "@types/eslint": "^8.56.5", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^8.0.0", "@typescript/vfs": "^1.5.0", "@vitest/coverage-v8": "^1.3.1", "console-fail-test": "^0.5.0", "cspell": "^8.6.0", "eslint": "^8.57.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-jsonc": "^2.13.0", "eslint-plugin-markdown": "^5.0.0", "eslint-plugin-n": "^17.0.0", "eslint-plugin-package-json": "^0.15.0", "eslint-plugin-perfectionist": "^2.6.0", "eslint-plugin-regexp": "^2.3.0", "eslint-plugin-vitest": "^0.4.0", "eslint-plugin-yml": "^1.12.2", "husky": "^9.0.11", "jsonc-eslint-parser": "^2.4.0", "knip": "^5.12.3", "lint-staged": "^15.2.2", "markdownlint": "^0.36.0", "markdownlint-cli": "^0.43.0", "prettier": "^3.1.1", "prettier-plugin-curly": "^0.3.0", "prettier-plugin-packagejson": "^2.4.7", "release-it": "^17.0.1", "sentences-per-line": "^0.2.1", "should-semantic-release": "^0.3.0", "tsup": "^8.0.2", "typedoc": "~0.24.8", "typedoc-plugin-coverage": "^3.0.0", "typedoc-plugin-custom-validation": "^2.0.2", "typedoc-plugin-konamimojisplosion": "^0.0.2", "typedoc-plugin-mdn-links": "^4.0.0", "typedoc-plugin-versions": "^0.2.4", "typescript": "~5.4.5", "vitest": "^1.3.1", "yaml-eslint-parser": "^1.2.2"}, "peerDependencies": {"typescript": ">=4.2.0"}, "packageManager": "pnpm@8.15.9", "engines": {"node": ">=16"}, "publishConfig": {"provenance": true}}