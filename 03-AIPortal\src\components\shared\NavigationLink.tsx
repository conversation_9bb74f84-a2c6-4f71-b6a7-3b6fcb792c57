import { Link } from "react-router-dom";
import { motion } from "framer-motion";

type Props = {
  to: string;
  bg: string;
  text: string;
  textColor: string;
  onClick?: () => Promise<void>;
};

const NavigationLink = (props: Props) => {
  return (
    <motion.div
      whileHover={{
        scale: 1.05,
        y: -2
      }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
    >
      <Link
        onClick={props.onClick}
        className="nav-link"
        to={props.to}
        style={{
          background: props.bg,
          color: props.textColor,
          boxShadow: "0 4px 15px rgba(0,0,0,0.2)",
          border: "1px solid rgba(255,255,255,0.1)",
          backdropFilter: "blur(10px)",
          fontWeight: "600",
          fontSize: "0.9rem"
        }}
      >
        {props.text}
      </Link>
    </motion.div>
  );
};

export default NavigationLink;
