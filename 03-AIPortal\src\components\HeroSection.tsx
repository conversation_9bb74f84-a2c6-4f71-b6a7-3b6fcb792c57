import { Box, Typography, But<PERSON>, Container, useMediaQuery, useTheme } from "@mui/material";
import { motion } from "framer-motion";
import TypingAnim from "./typer/TypingAnim";

const HeroSection = () => {
  const theme = useTheme();
  const isBelowMd = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Container maxWidth="lg" sx={{ py: 8 }}>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          textAlign: "center",
          minHeight: "80vh",
          justifyContent: "center",
          position: "relative"
        }}
      >
        {/* Floating Elements */}
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 5, -5, 0]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{
            position: "absolute",
            top: "10%",
            right: "10%",
            opacity: 0.3
          }}
        >
          <Box
            sx={{
              width: 100,
              height: 100,
              borderRadius: "50%",
              background: "linear-gradient(45deg, #ff6b6b, #4ecdc4)",
              filter: "blur(20px)"
            }}
          />
        </motion.div>

        <motion.div
          animate={{
            y: [20, -20, 20],
            rotate: [0, -5, 5, 0]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{
            position: "absolute",
            bottom: "20%",
            left: "5%",
            opacity: 0.2
          }}
        >
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: "50%",
              background: "linear-gradient(45deg, #4ecdc4, #45b7d1)",
              filter: "blur(15px)"
            }}
          />
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <Typography
            variant="h2"
            sx={{
              mb: 2,
              fontWeight: "bold",
              background: "linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1)",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontSize: isBelowMd ? "2.5rem" : "3.5rem"
            }}
          >
            Saomai AI Portal
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.3 }}
        >
          <TypingAnim />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.6 }}
        >
          <Typography
            variant="h6"
            sx={{
              mt: 4,
              mb: 6,
              color: "rgba(255,255,255,0.8)",
              maxWidth: "600px",
              lineHeight: 1.6
            }}
          >
            Trải nghiệm sức mạnh của trí tuệ nhân tạo với công nghệ tiên tiến nhất. 
            Được phát triển bởi Saomai Solution Group cho tương lai số hóa.
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        >
          <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap", justifyContent: "center" }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                background: "linear-gradient(45deg, #ff6b6b, #4ecdc4)",
                color: "white",
                px: 4,
                py: 2,
                fontSize: "1.1rem",
                borderRadius: 3,
                textTransform: "none",
                boxShadow: "0 4px 20px rgba(255,107,107,0.3)",
                "&:hover": {
                  background: "linear-gradient(45deg, #ff5252, #26a69a)",
                  transform: "translateY(-2px)",
                  boxShadow: "0 6px 25px rgba(255,107,107,0.4)"
                },
                transition: "all 0.3s ease"
              }}
              href="/chat"
            >
              Khám phá AI Chat
            </Button>
            
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: "rgba(255,255,255,0.3)",
                color: "white",
                px: 4,
                py: 2,
                fontSize: "1.1rem",
                borderRadius: 3,
                textTransform: "none",
                backdropFilter: "blur(10px)",
                background: "rgba(255,255,255,0.1)",
                "&:hover": {
                  borderColor: "#4ecdc4",
                  background: "rgba(78,205,196,0.1)",
                  transform: "translateY(-2px)"
                },
                transition: "all 0.3s ease"
              }}
            >
              Tìm hiểu thêm
            </Button>
          </Box>
        </motion.div>

        {/* Tech Icons */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.2 }}
        >
        </motion.div>
      </Box>
    </Container>
  );
};

export default HeroSection;
