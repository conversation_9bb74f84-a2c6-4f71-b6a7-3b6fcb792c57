# Core FastAPI dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Authentication & Security
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6

# Data validation
pydantic>=2.5.0
pydantic_settings>=2.1.0
pydantic[email]>=2.5.0

# Database
motor>=3.3.0  # Async MongoDB driver
pymongo>=4.6.0

# AI & ML
google-generativeai>=0.3.0
langchain>=0.1.0
langchain-ollama>=0.1.0
langchain-community>=0.0.10
langgraph>=0.0.20
sentence-transformers>=3.0.0  # Required for BGE-M3 embedding in qdrant_simple.py

# Vector stores & Search
qdrant-client>=1.7.0
requests>=2.31.0  # For Elasticsearch HTTP requests

# Data processing
pandas>=2.1.0
dateparser>=1.2.0

# Job scheduling
apscheduler>=3.10.0

# Transformers (required by sentence-transformers)
transformers>=4.40.2
torch>=2.6.0  # Required by sentence-transformers, upgraded for security
accelerate>=0.29.3

# Development dependencies (uncomment for dev)
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# isort>=5.12.0
