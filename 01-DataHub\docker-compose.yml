
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.13.2
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
    ports:
      - "9200:9200"
    volumes:
      - ./volumes/elasticsearch:/usr/share/elasticsearch/data
    restart: unless-stopped
    networks:
      - kong-net

  kibana:
    image: docker.elastic.co/kibana/kibana:8.13.2
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://host.docker.internal:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - kong-net

  qdrant:
    image: qdrant/qdrant
    container_name: qdrant
    ports:
      - "6333:6333"
    volumes:
      - ./volumes/qdrant:/qdrant/storage
    restart: unless-stopped
    networks:
      - kong-net

  n8n:
    image: n8nio/n8n
    container_name: n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_FUNCTION_ALLOW_BUILTIN=*
      - N8N_FUNCTION_ALLOW_EXTERNAL=*
      - N8N_DISABLE_EXTERNAL_EXECUTION_SANDBOX=true
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_SECURE_COOKIE=false
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_RUNNERS_ENABLED=true
      - N8N_FEATURE_FLAGS=FUNCTION_NODES

    volumes:
      - ./volumes/n8n:/home/<USER>/.n8n
      - D:/SSG_AI/DataHub:/data/pdfs
    restart: unless-stopped
    networks:
      - kong-net

  ollama:
    image: ollama/ollama
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ./volumes/ollama:/root/.ollama
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    restart: unless-stopped
    networks:
      - kong-net
volumes:
  pdf_storage: {} 

networks:
  kong-net:
    driver: bridge