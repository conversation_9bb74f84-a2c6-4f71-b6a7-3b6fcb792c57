{"name": "motion-dom", "version": "12.23.5", "author": "<PERSON>", "license": "MIT", "repository": "https://github.com/motiondivision/motion", "main": "./dist/cjs/index.js", "types": "./dist/index.d.ts", "module": "./dist/es/index.mjs", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/cjs/index.js", "import": "./dist/es/index.mjs", "default": "./dist/cjs/index.js"}}, "dependencies": {"motion-utils": "^12.23.2"}, "scripts": {"clean": "rm -rf types dist lib", "build": "yarn clean && tsc -p . && rollup -c && node ./scripts/check-bundle.js", "dev": "concurrently -c blue,red -n tsc,rollup --kill-others \"tsc --watch -p . --preserveWatchOutput\" \"rollup --config --watch --no-watch.clearScreen\"", "test": "jest --config jest.config.json --max-workers=2", "measure": "rollup -c ./rollup.size.config.mjs"}, "bundlesize": [{"path": "./dist/size-rollup-style-effect.js", "maxSize": "2.9 kB"}, {"path": "./dist/size-rollup-motion-value.js", "maxSize": "1.8 kB"}], "gitHead": "db66bee31fe698bf43833d83ec859ee3295a36e4"}