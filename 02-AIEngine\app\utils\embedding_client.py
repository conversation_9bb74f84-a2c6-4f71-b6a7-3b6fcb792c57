"""
HTTP client for embedding-server
"""
import requests
import logging
from typing import List
from app.config.index import settings

logger = logging.getLogger(__name__)

class EmbeddingClient:
    """Client to communicate with embedding-server"""
    
    def __init__(self, base_url: str = "http://embedding-server:8000"):
        self.base_url = base_url
        
    def embed_query(self, text: str) -> List[float]:
        """Get embedding for a single text"""
        try:
            response = requests.post(
                f"{self.base_url}/embed",
                json={"text": text},
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            return result["embedding"]
        except Exception as e:
            logger.error(f"Failed to get embedding from server: {e}")
            raise
            
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for multiple texts"""
        try:
            response = requests.post(
                f"{self.base_url}/embed_batch",
                json={"texts": texts},
                timeout=60
            )
            response.raise_for_status()
            result = response.json()
            return result["embeddings"]
        except Exception as e:
            logger.error(f"Failed to get batch embeddings from server: {e}")
            raise
            
    def health_check(self) -> bool:
        """Check if embedding server is healthy"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

# Global instance
_embedding_client = None

def get_embedding_client() -> EmbeddingClient:
    """Get global embedding client instance"""
    global _embedding_client
    if _embedding_client is None:
        _embedding_client = EmbeddingClient()
    return _embedding_client
