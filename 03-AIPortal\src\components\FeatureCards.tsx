import { Box, Typography, Container, Grid, Card, CardContent, useMediaQuery, useTheme } from "@mui/material";
import { motion } from "framer-motion";

const FeatureCards = () => {
  const theme = useTheme();
  const isBelowMd = useMediaQuery(theme.breakpoints.down("md"));

  const features = [
    {
      title: "AI Chat Thông minh",
      description: "Trò chuyện với AI được huấn luyện trên dữ liệu chuyên biệt của Saomai Solution Group",
      icon: "💬",
      gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      stats: "99.9% Uptime"
    },
    {
      title: "Xử lý Ngôn ngữ Tự nhiên",
      description: "Hiểu và phản hồi tiếng Việt một cách tự nhiên và chính xác",
      icon: "🧠",
      gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      stats: "Hỗ trợ Tiếng Việt"
    },
    {
      title: "Tích hợp Doanh nghiệp",
      description: "Dễ dàng tích hợp vào hệ thống hiện có của doanh nghiệp",
      icon: "🏢",
      gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      stats: "API Ready"
    },
    {
      title: "Bảo mật Cao",
      description: "Đảm bảo an toàn dữ liệu với các tiêu chuẩn bảo mật quốc tế",
      icon: "🔒",
      gradient: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      stats: "ISO 27001"
    },
    {
      title: "Phân tích Thông minh",
      description: "Cung cấp insights và analytics từ các cuộc hội thoại",
      icon: "📊",
      gradient: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      stats: "Real-time Analytics"
    },
    {
      title: "Hỗ trợ 24/7",
      description: "Hệ thống hoạt động liên tục, sẵn sàng phục vụ mọi lúc",
      icon: "⏰",
      gradient: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      stats: "24/7 Support"
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 8 }}>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <Typography
          variant="h3"
          sx={{
            textAlign: "center",
            mb: 2,
            fontWeight: "bold",
            background: "linear-gradient(45deg, #4ecdc4, #45b7d1)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent"
          }}
        >
          Tính năng nổi bật
        </Typography>
        
        <Typography
          variant="h6"
          sx={{
            textAlign: "center",
            mb: 6,
            color: "rgba(255,255,255,0.7)",
            maxWidth: "600px",
            mx: "auto"
          }}
        >
          Khám phá những tính năng mạnh mẽ được thiết kế đặc biệt cho doanh nghiệp Việt Nam
        </Typography>
      </motion.div>

      <Grid container spacing={4}>
        {features.map((feature, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ 
                y: -10,
                transition: { duration: 0.3 }
              }}
            >
              <Card
                sx={{
                  height: "100%",
                  background: "linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255,255,255,0.1)",
                  borderRadius: 3,
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    background: "linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%)",
                    borderColor: "rgba(255,255,255,0.2)",
                    boxShadow: "0 20px 40px rgba(0,0,0,0.3)"
                  }
                }}
              >
                <CardContent sx={{ p: 4, height: "100%", display: "flex", flexDirection: "column" }}>
                  {/* Icon with gradient background */}
                  <Box
                    sx={{
                      width: 80,
                      height: 80,
                      borderRadius: "50%",
                      background: feature.gradient,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      fontSize: "2rem",
                      mb: 3,
                      mx: "auto",
                      boxShadow: "0 8px 25px rgba(0,0,0,0.2)"
                    }}
                  >
                    {feature.icon}
                  </Box>

                  {/* Title */}
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 2,
                      fontWeight: "bold",
                      color: "white",
                      textAlign: "center"
                    }}
                  >
                    {feature.title}
                  </Typography>

                  {/* Description */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(255,255,255,0.7)",
                      lineHeight: 1.6,
                      textAlign: "center",
                      flex: 1,
                      mb: 3
                    }}
                  >
                    {feature.description}
                  </Typography>

                  {/* Stats Badge */}
                  <Box
                    sx={{
                      background: feature.gradient,
                      borderRadius: 2,
                      px: 2,
                      py: 1,
                      textAlign: "center",
                      mt: "auto"
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        color: "white",
                        fontWeight: "bold",
                        fontSize: "0.8rem"
                      }}
                    >
                      {feature.stats}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Interactive Demo Section */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        viewport={{ once: true }}
      >
        <Box
          sx={{
            mt: 8,
            p: 6,
            background: "linear-gradient(135deg, rgba(255,107,107,0.1) 0%, rgba(78,205,196,0.1) 100%)",
            backdropFilter: "blur(10px)",
            borderRadius: 4,
            border: "1px solid rgba(255,255,255,0.1)",
            textAlign: "center"
          }}
        >
          <Typography
            variant="h4"
            sx={{
              mb: 3,
              fontWeight: "bold",
              background: "linear-gradient(45deg, #ff6b6b, #4ecdc4)",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent"
            }}
          >
            Trải nghiệm tại Triển lãm
          </Typography>
          
          <Typography
            variant="h6"
            sx={{
              mb: 4,
              color: "rgba(255,255,255,0.8)",
              maxWidth: "800px",
              mx: "auto"
            }}
          >
            Hãy đến gian hàng của Saomai Solution Group để trải nghiệm trực tiếp 
            sức mạnh của AI và khám phá những giải pháp công nghệ tiên tiến nhất
          </Typography>

          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 4,
              flexWrap: "wrap",
              mt: 4
            }}
          >
            <Box sx={{ textAlign: "center" }}>
              <Typography variant="h3" sx={{ color: "#ff6b6b", fontWeight: "bold" }}>
                100+
              </Typography>
              <Typography variant="body2" sx={{ color: "rgba(255,255,255,0.7)" }}>
                Doanh nghiệp tin tưởng
              </Typography>
            </Box>
            
            <Box sx={{ textAlign: "center" }}>
              <Typography variant="h3" sx={{ color: "#4ecdc4", fontWeight: "bold" }}>
                99.9%
              </Typography>
              <Typography variant="body2" sx={{ color: "rgba(255,255,255,0.7)" }}>
                Độ chính xác
              </Typography>
            </Box>
            
            <Box sx={{ textAlign: "center" }}>
              <Typography variant="h3" sx={{ color: "#45b7d1", fontWeight: "bold" }}>
                24/7
              </Typography>
              <Typography variant="body2" sx={{ color: "rgba(255,255,255,0.7)" }}>
                Hỗ trợ liên tục
              </Typography>
            </Box>
          </Box>
        </Box>
      </motion.div>
    </Container>
  );
};

export default FeatureCards;
