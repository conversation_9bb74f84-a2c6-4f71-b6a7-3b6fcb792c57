{"name": "lowlight", "version": "1.20.0", "description": "Virtual syntax highlighting for virtual DOMs and non-HTML things", "license": "MIT", "keywords": ["syntax", "code", "ast", "virtual", "dom", "highlight", "highlighting"], "repository": "wooorm/lowlight", "bugs": "https://github.com/wooorm/lowlight/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON>i <<EMAIL>>"], "files": ["index.js", "lib/core.js"], "dependencies": {"fault": "^1.0.0", "highlight.js": "~10.7.0"}, "devDependencies": {"browserify": "^17.0.0", "chalk": "^4.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "rehype": "^11.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "tinyify": "^3.0.0", "unist-util-remove-position": "^3.0.0", "xo": "^0.38.0"}, "scripts": {"generate": "node script/build-registry", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "build-bundle": "browserify index.js -s lowlight > lowlight.js", "build-mangle": "browserify index.js -s lowlight -p tinyify > lowlight.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test/index.js", "test": "npm run generate && npm run format && npm run build && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "rules": {"unicorn/no-array-callback-reference": "off", "unicorn/no-fn-reference-in-iterator": "off", "unicorn/prefer-optional-catch-binding": "off", "unicorn/prefer-includes": "off", "guard-for-in": "off"}, "ignores": ["lowlight.js"]}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}}