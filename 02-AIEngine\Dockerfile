# Use Python slim instead of alpine for better compatibility with ML libraries
FROM python:3.11-slim

# Install system dependencies needed for ML libraries
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .

RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5000", "--reload"]

# Alternative: Use PyTorch base image if you need CUDA support
# FROM pytorch/pytorch:2.2.2-cuda11.8-cudnn8-runtime
# WORKDIR /app
# COPY requirements.txt .
# RUN pip install --upgrade pip && \
#     pip install --no-cache-dir -r requirements.txt
# COPY . .
# EXPOSE 5000
# CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5000"]
