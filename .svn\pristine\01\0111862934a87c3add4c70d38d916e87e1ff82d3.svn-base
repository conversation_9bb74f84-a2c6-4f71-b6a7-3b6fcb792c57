{"name": "mdast-util-mdx-expression", "version": "2.0.1", "description": "mdast extension to parse and serialize MDX (or MDX.js) expressions", "license": "MIT", "keywords": ["unist", "mdast", "mdast-util", "util", "utility", "markdown", "markup", "mdx", "mdxjs", "expression", "extension"], "repository": "syntax-tree/mdast-util-mdx-expression", "bugs": "https://github.com/syntax-tree/mdast-util-mdx-expression/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "complex-types.d.ts", "index.d.ts", "index.js"], "dependencies": {"@types/estree-jsx": "^1.0.0", "@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "devlop": "^1.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0"}, "devDependencies": {"@types/acorn": "^4.0.0", "@types/node": "^22.0.0", "acorn": "^8.0.0", "c8": "^10.0.0", "micromark-extension-mdx-expression": "^3.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "unist-util-remove-position": "^5.0.0", "xo": "^0.59.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api-prod": "node --conditions production test.js", "test-api-dev": "node --conditions development test.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/ban-types": "off", "@typescript-eslint/consistent-type-definitions": "off"}}], "prettier": true, "rules": {"unicorn/prefer-at": "off"}}}