import { TypeAnimation } from "react-type-animation";
import { useMediaQuery, useTheme } from "@mui/material";

const TypingAnim = () => {
  const theme = useTheme();
  const isBelowMd = useMediaQuery(theme.breakpoints.down("md"));
  const isBelowSm = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <TypeAnimation
      sequence={[
        "Trí tuệ nhân tạo cho doanh nghiệp 🚀",
        2000,
        "Giải pháp AI tiên tiến nhất 🤖",
        2000,
        "Công nghệ tương lai hôm nay 💡",
        2000,
        "Saomai Solution Group 🌟",
        2000,
        "Khám phá sức mạnh AI 🔥",
        2000,
      ]}
      speed={50}
      style={{
        fontSize: isBelowSm ? "1.8rem" : isBelowMd ? "2.5rem" : "3.5rem",
        color: "white",
        display: "inline-block",
        textShadow: "2px 2px 30px rgba(0,0,0,0.8)",
        fontWeight: "bold",
        background: "linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1)",
        backgroundClip: "text",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        textAlign: "center",
        lineHeight: 1.2,
        maxWidth: "90vw"
      }}
      repeat={Infinity}
    />
  );
};

export default TypingAnim;
