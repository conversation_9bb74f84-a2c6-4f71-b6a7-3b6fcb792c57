import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Model configuration
    MODEL_NAME: str = "sentence-transformers/all-MiniLM-L6-v2"  # Smaller, more reliable model
    MODEL_CACHE_DIR: Optional[str] = "./models"
    DEVICE: str = "auto"  # auto, cpu, cuda
    
    # Server configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 1
    
    # Text processing configuration
    MAX_TEXT_LENGTH: int = 8192  # Maximum text length for processing
    DEFAULT_CHUNK_SIZE: int = 384  # Optimal for retrieval (roughly 100-120 tokens)
    DEFAULT_OVERLAP: int = 64      # ~15-20% overlap for context continuity
    MIN_CHUNK_SIZE: int = 50       # Minimum for embedding compatibility (>= 4 tokens)
    TARGET_CHUNK_RATIO: float = 0.75  # Target 75% of max size for better balance

    # Embedding compatibility settings
    MIN_EMBEDDING_TOKENS: int = 4   # Minimum tokens required by embedding models
    MIN_MEANINGFUL_WORDS: int = 3   # Minimum words for meaningful content
    
    # Performance settings
    BATCH_SIZE: int = 32
    MAX_CONCURRENT_REQUESTS: int = 10
    
    # Logging
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create global settings instance
settings = Settings()
