{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.9", "axios": "^1.5.0", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.11.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.16.0", "react-syntax-highlighter": "^15.6.1", "react-type-animation": "^3.1.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "concurrently": "^9.1.2", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "nodemon": "^3.1.9", "typescript": "^5.8.3", "vite": "^4.4.5"}}