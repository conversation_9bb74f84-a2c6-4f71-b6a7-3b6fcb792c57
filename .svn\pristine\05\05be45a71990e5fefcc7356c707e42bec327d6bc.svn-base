{"version": 3, "sources": ["../../highlight.js/lib/languages/rsl.js"], "sourcesContent": ["/*\nLanguage: RenderMan RSL\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://renderman.pixar.com/resources/RenderMan_20/shadingLanguage.html\nCategory: graphics\n*/\n\nfunction rsl(hljs) {\n  return {\n    name: 'RenderMan RSL',\n    keywords: {\n      keyword:\n        'float color point normal vector matrix while for if do return else break extern continue',\n      built_in:\n        'abs acos ambient area asin atan atmosphere attribute calculatenormal ceil cellnoise ' +\n        'clamp comp concat cos degrees depth Deriv diffuse distance Du Dv environment exp ' +\n        'faceforward filterstep floor format fresnel incident length lightsource log match ' +\n        'max min mod noise normalize ntransform opposite option phong pnoise pow printf ' +\n        'ptlined radians random reflect refract renderinfo round setcomp setxcomp setycomp ' +\n        'setzcomp shadow sign sin smoothstep specular specularbrdf spline sqrt step tan ' +\n        'texture textureinfo trace transform vtransform xcomp ycomp zcomp'\n    },\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$'\n      },\n      {\n        className: 'class',\n        beginKeywords: 'surface displacement light volume imager',\n        end: '\\\\('\n      },\n      {\n        beginKeywords: 'illuminate illuminance gather',\n        end: '\\\\('\n      }\n    ]\n  };\n}\n\nmodule.exports = rsl;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SACE;AAAA,UACF,UACE;AAAA,QAOJ;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}