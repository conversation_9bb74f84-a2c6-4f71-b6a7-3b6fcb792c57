import { Box, Typography, Container, Grid, useMediaQuery, useTheme } from "@mui/material";
import { motion } from "framer-motion";

const TechShowcase = () => {
  const theme = useTheme();
  const isBelowMd = useMediaQuery(theme.breakpoints.down("md"));

  const techItems = [
    {
      title: "Machine Learning",
      description: "Thuật toán học máy tiên tiến",
      icon: "🤖",
      color: "#ff6b6b"
    },
    {
      title: "Natural Language Processing",
      description: "Xử lý ngôn ngữ tự nhiên",
      icon: "💬",
      color: "#4ecdc4"
    },
    {
      title: "Computer Vision",
      description: "Thị giác máy tính thông minh",
      icon: "👁️",
      color: "#45b7d1"
    },
    {
      title: "Deep Learning",
      description: "Mạng neural sâu",
      icon: "🧠",
      color: "#96ceb4"
    },
    {
      title: "Big Data Analytics",
      description: "<PERSON><PERSON> tích dữ liệu lớn",
      icon: "📊",
      color: "#feca57"
    },
    {
      title: "Cloud Computing",
      description: "Điện toán đám mây",
      icon: "☁️",
      color: "#ff9ff3"
    }
  ];

  return (
    <Box
      sx={{
        py: 8,
        background: "rgba(255,255,255,0.02)",
        backdropFilter: "blur(10px)",
        borderTop: "1px solid rgba(255,255,255,0.1)",
        borderBottom: "1px solid rgba(255,255,255,0.1)"
      }}
    >
      <Container maxWidth="lg">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Typography
            variant="h3"
            sx={{
              textAlign: "center",
              mb: 2,
              fontWeight: "bold",
              background: "linear-gradient(45deg, #ff6b6b, #4ecdc4)",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent"
            }}
          >
            Công nghệ tiên tiến
          </Typography>
          
          <Typography
            variant="h6"
            sx={{
              textAlign: "center",
              mb: 6,
              color: "rgba(255,255,255,0.7)",
              maxWidth: "600px",
              mx: "auto"
            }}
          >
            Khám phá các công nghệ AI hàng đầu được tích hợp trong hệ thống của chúng tôi
          </Typography>
        </motion.div>

        <Grid container spacing={4}>
          {techItems.map((item, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ 
                  scale: 1.05,
                  transition: { duration: 0.3 }
                }}
              >
                <Box
                  sx={{
                    p: 4,
                    height: "100%",
                    background: "linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)",
                    backdropFilter: "blur(10px)",
                    borderRadius: 3,
                    border: "1px solid rgba(255,255,255,0.1)",
                    textAlign: "center",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      background: "linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%)",
                      borderColor: item.color,
                      boxShadow: `0 8px 32px ${item.color}20`
                    }
                  }}
                >
                  <Box
                    sx={{
                      fontSize: "3rem",
                      mb: 2,
                      display: "inline-block",
                      p: 2,
                      borderRadius: "50%",
                      background: `linear-gradient(45deg, ${item.color}20, ${item.color}10)`,
                      border: `2px solid ${item.color}30`
                    }}
                  >
                    {item.icon}
                  </Box>
                  
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 2,
                      fontWeight: "bold",
                      color: item.color
                    }}
                  >
                    {item.title}
                  </Typography>
                  
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(255,255,255,0.7)",
                      lineHeight: 1.6
                    }}
                  >
                    {item.description}
                  </Typography>
                </Box>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Central AI Image */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              mt: 8,
              display: "flex",
              justifyContent: "center",
              position: "relative"
            }}
          >
            <Box
              sx={{
                position: "relative",
                display: "inline-block"
              }}
            >
              <img
                src="nuclear.jpg"
                alt="AI Technology"
                style={{
                  width: isBelowMd ? "300px" : "400px",
                  height: isBelowMd ? "200px" : "250px",
                  objectFit: "cover",
                  borderRadius: "20px",
                  boxShadow: "0 20px 60px rgba(0,0,0,0.3)",
                  border: "2px solid rgba(255,255,255,0.1)"
                }}
              />
              
              {/* Glowing effect */}
              <Box
                sx={{
                  position: "absolute",
                  top: "-10px",
                  left: "-10px",
                  right: "-10px",
                  bottom: "-10px",
                  background: "linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4)",
                  borderRadius: "25px",
                  opacity: 0.3,
                  filter: "blur(20px)",
                  zIndex: -1,
                  animation: "pulse 3s ease-in-out infinite"
                }}
              />
            </Box>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default TechShowcase;
