@import url("https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400;500;600;800&family=Work+Sans:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&display=swap");

html,
body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
  color: white;
  font-family: "Inter", "Roboto Slab", serif;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #ff5252, #26a69a);
}

.image-inverted {
  filter: invert(100%);
}

.nav-link {
  font-weight: 600;
  text-transform: uppercase;
  margin-right: 10px;
  margin-left: 10px;
  padding: 8px 20px;
  border-radius: 10px;
  text-decoration: none;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(255,255,255,0.1);
  transform: translateY(-2px);
}

.MuiOutlinedInput-input-root.Mui-focused,
.MuiOutlinedInput-notchedOutline {
  border-color: white !important;
}

/* Enhanced animations */
.rotate {
  animation: rotation 8s infinite linear;
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(5deg);
  }
  66% {
    transform: translateY(10px) rotate(-5deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255,107,107,0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(78,205,196,0.5);
  }
}

/* Glassmorphism effect */
.glass {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Button hover effects */
.btn-glow {
  transition: all 0.3s ease;
}

.btn-glow:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255,107,107,0.4);
}

/* Loading animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Responsive text */
@media (max-width: 768px) {
  .nav-link {
    margin: 5px;
    padding: 6px 15px;
    font-size: 0.9rem;
  }
}

/* Smooth transitions for all elements */
* {
  transition: all 0.3s ease;
}
