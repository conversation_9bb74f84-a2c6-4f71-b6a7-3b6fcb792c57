import { Box, useMediaQuery, useTheme, Typography, <PERSON><PERSON>, <PERSON>rid, <PERSON>, Card<PERSON>ontent, Container } from "@mui/material";
import { motion } from "framer-motion";
import TypingAnim from "../components/typer/TypingAnim";
import Footer from "../components/footer/Footer";
import TechShowcase from "../components/TechShowcase";
import HeroSection from "../components/HeroSection";
import FeatureCards from "../components/FeatureCards";

const Home = () => {
  const theme = useTheme();
  const isBelowMd = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Box
      width="100%"
      minHeight="100vh"
      sx={{
        background: `
          linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%),
          radial-gradient(circle at 20% 50%, #120458 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, #ff6b6b 0%, transparent 50%),
          radial-gradient(circle at 40% 80%, #4ecdc4 0%, transparent 50%)
        `,
        backgroundAttachment: 'fixed',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          background: `
            radial-gradient(circle at 25% 25%, #ff6b6b 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, #4ecdc4 0%, transparent 50%)
          `,
          animation: 'float 20s ease-in-out infinite'
        }}
      />

      {/* Hero Section */}
      <HeroSection />

      {/* Tech Showcase */}
      <TechShowcase />

      {/* Feature Cards */}
      <FeatureCards />

      {/* CTA Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              textAlign: 'center',
              background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
              backdropFilter: 'blur(10px)',
              borderRadius: 4,
              p: 6,
              border: '1px solid rgba(255,255,255,0.1)',
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
            }}
          >
            <Typography
              variant="h3"
              sx={{
                mb: 3,
                background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: 'bold'
              }}
            >
              Sẵn sàng trải nghiệm AI?
            </Typography>
            <Typography variant="h6" sx={{ mb: 4, color: 'rgba(255,255,255,0.8)' }}>
              Khám phá sức mạnh của trí tuệ nhân tạo với Saomai Solution Group
            </Typography>
            <Button
              variant="contained"
              size="large"
              sx={{
                background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
                color: 'white',
                px: 4,
                py: 2,
                fontSize: '1.2rem',
                borderRadius: 3,
                textTransform: 'none',
                boxShadow: '0 4px 20px rgba(255,107,107,0.3)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #ff5252, #26a69a)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 6px 25px rgba(255,107,107,0.4)'
                },
                transition: 'all 0.3s ease'
              }}
              href="/chat"
            >
              Bắt đầu Chat ngay
            </Button>
          </Box>
        </motion.div>
      </Container>

      <Footer />
    </Box>
  );
};

export default Home;
