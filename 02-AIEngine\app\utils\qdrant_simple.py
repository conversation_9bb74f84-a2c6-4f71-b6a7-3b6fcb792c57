"""
Simple sync Qdrant utilities without async dependencies
"""
from qdrant_client import QdrantClient
from langchain_community.vectorstores import Qdrant as QdrantVectorStore
from app.config.index import settings
import logging
from typing import List, Any
import requests
import json

logger = logging.getLogger(__name__)

# Global instances for reuse
_qdrant_client = None
_embedding_model = None
_vectorstore = None


def get_qdrant_client_simple() -> QdrantClient:
    """Get simple Qdrant client"""
    global _qdrant_client
    if _qdrant_client is None:
        try:
            _qdrant_client = QdrantClient(
                host=settings.HOST_QDRANT, 
                port=6333,
                timeout=30
            )
            logger.info("Simple Qdrant client created successfully")
        except Exception as e:
            logger.error(f"Failed to create simple Qdrant client: {e}")
            raise
    return _qdrant_client


def get_embedding_model_simple():
    """Get simple embedding model using BGE-M3 via embedding server"""
    global _embedding_model
    if _embedding_model is None:
        try:
            logger.info("Using BGE-M3 embedding server")
            _embedding_model = True  # Just a flag to indicate it's initialized
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise

    class BGEM3Embedding:
        def embed_query(self, text: str):
            """Embed a single query using BGE-M3 via embedding server"""
            try:
                # Use embedding server instead of direct model
                response = requests.post(
                    "http://192.168.60.31:8080/embed",
                    json={"text": text, "normalize": True},
                    timeout=30
                )
                response.raise_for_status()
                result = response.json()
                # API returns embeddings directly as array
                return result["embeddings"]
            except Exception as e:
                logger.error(f"BGE-M3 embedding server failed: {e}")
                raise

    # Return instance of the embedding class
    return BGEM3Embedding()


def get_qdrant_vectorstore_simple() -> QdrantVectorStore:
    """Get simple Qdrant vectorstore with BGE-M3 embedding"""
    global _vectorstore
    if _vectorstore is None:
        try:
            client = get_qdrant_client_simple()
            embedding_model = get_embedding_model_simple()

            _vectorstore = QdrantVectorStore(
                client=client,
                collection_name=settings.QDRANT_COLLECTION,
                embeddings=embedding_model,
                content_payload_key="text",  # Use 'text' key as per actual data structure
                metadata_payload_key="source"  # Use 'source' key as per actual data structure
            )
            logger.info("Simple Qdrant vectorstore with BGE-M3 created successfully")
        except Exception as e:
            logger.error(f"Failed to create simple Qdrant vectorstore with BGE-M3: {e}")
            raise
    return _vectorstore


def search_qdrant_simple(question: str) -> List[Any]:
    """
    Simple sync Qdrant search using BGE-M3 embedding
    """
    try:
        # Use direct BGE-M3 embedding approach instead of LangChain vectorstore
        logger.info("Using BGE-M3 embedding for Qdrant search...")
        return search_qdrant_fallback(question)

    except Exception as e:
        logger.error(f"Simple Qdrant search with BGE-M3 failed: {e}")
        # Try with LangChain vectorstore as fallback
        try:
            logger.info("Attempting LangChain vectorstore fallback...")
            return search_qdrant_langchain_fallback(question)
        except Exception as fallback_error:
            logger.error(f"LangChain fallback also failed: {fallback_error}")
            return []


def search_qdrant_langchain_fallback(question: str) -> List[Any]:
    """
    Fallback using LangChain vectorstore with nomic-embed-text
    """
    try:
        # Get vectorstore
        vectorstore = get_qdrant_vectorstore_simple()

        # Create retriever with more lenient settings
        retriever = vectorstore.as_retriever(
            search_type="similarity",  # Use similarity without threshold first
            search_kwargs={
                "k": 8,  # Get more results
                "fetch_k": 20  # Increase fetch_k for better candidate pool
            }
        )

        # Perform search
        logger.info(f"Performing LangChain Qdrant search for: {question[:50]}...")
        docs = retriever.invoke(question)

        logger.info(f"LangChain Qdrant search completed, found {len(docs)} documents")
        return docs

    except Exception as e:
        logger.error(f"LangChain Qdrant search failed: {e}")
        return []


def search_qdrant_fallback(question: str) -> List[Any]:
    """
    Fallback Qdrant search with minimal settings using BGE-M3 embedding
    """
    try:
        # Get embedding model
        embedding_model = get_embedding_model_simple()

        # Get client directly
        client = get_qdrant_client_simple()

        # Get embedding for the question using BGE-M3
        question_vector = embedding_model.embed_query(question)

        # Direct search using client with improved parameters
        logger.info(f"Searching in collection: {settings.QDRANT_COLLECTION}")
        logger.info(f"Query vector dimensions: {len(question_vector)}")

        # First try without score threshold to see if we get any results
        search_result = client.search(
            collection_name=settings.QDRANT_COLLECTION,
            query_vector=question_vector,
            limit=10,  # Increase limit for better coverage
            with_payload=True,
            with_vectors=False  # Don't return vectors to save bandwidth
        )

        logger.info(f"Raw search found {len(search_result)} results")

        # Log scores to understand the distribution
        if search_result:
            scores = [point.score for point in search_result]
            logger.info(f"Score range: {min(scores):.4f} - {max(scores):.4f}")

            # Filter by a more reasonable threshold
            filtered_results = [point for point in search_result if point.score >= 0.3]
            logger.info(f"After 0.3 threshold: {len(filtered_results)} results")
            search_result = filtered_results
        else:
            logger.warning("No results found even without score threshold")
        print(f"Search result: {search_result}")

        # Convert to LangChain document format
        docs = []
        for point in search_result:
            if point.payload:
                # Try both 'content' and 'text' keys for compatibility
                content = point.payload.get('content', '') or point.payload.get('text', '')
                metadata = point.payload.get('metadata', {}) or point.payload.get('source', '')

                # Only add if we have actual content
                if content and content.strip():
                    # Create a simple document-like object
                    doc = type('Document', (), {
                        'page_content': content,
                        'metadata': metadata if isinstance(metadata, dict) else {'source': metadata}
                    })()
                    docs.append(doc)
                    logger.debug(f"Added document with {len(content)} characters")

        logger.info(f"Fallback Qdrant search with BGE-M3 completed, found {len(docs)} documents")
        return docs

    except Exception as e:
        logger.error(f"Fallback Qdrant search with BGE-M3 failed: {e}")
        return []


def health_check_qdrant() -> bool:
    """
    Check Qdrant health and collection status
    """
    try:
        client = get_qdrant_client_simple()

        # Check if collection exists
        collections = client.get_collections()
        collection_names = [col.name for col in collections.collections]

        if settings.QDRANT_COLLECTION not in collection_names:
            logger.error(f"Collection '{settings.QDRANT_COLLECTION}' not found in Qdrant")
            return False

        # Get collection info
        collection_info = client.get_collection(settings.QDRANT_COLLECTION)
        logger.info(f"Qdrant collection '{settings.QDRANT_COLLECTION}' status: {collection_info.status}")
        logger.info(f"Points count: {collection_info.points_count}")
        logger.info(f"Vector size: {collection_info.config.params.vectors.size}")

        # Check if vector size matches BGE-M3 embedding (1024) or other models
        vector_size = collection_info.config.params.vectors.size
        if vector_size == 1024:
            logger.info("Collection appears to use 1024-dimensional embeddings (compatible with BGE-M3)")
        elif vector_size == 768:
            logger.info("Collection uses 768-dimensional embeddings (Gemini/other models)")
        elif vector_size == 4096:
            logger.info("Collection uses 4096-dimensional embeddings (other large models)")
        else:
            logger.info(f"Collection uses {vector_size}-dimensional embeddings")

        # Warning if dimension mismatch
        if vector_size != 1024:
            logger.error(f"🚨 CRITICAL: Dimension mismatch detected!")
            logger.error(f"Collection '{settings.QDRANT_COLLECTION}' expects {vector_size} dimensions")
            logger.error(f"BGE-M3 model produces 1024 dimensions")
            logger.error("This WILL cause search to return 0 results!")
            logger.error("SOLUTION: Recreate collection with 1024 dimensions or use different embedding model")
            return False  # Return False to indicate critical issue

        return collection_info.status == "green"

    except Exception as e:
        logger.error(f"Qdrant health check failed: {e}")
        return False


def close_qdrant_connections():
    """Close Qdrant connections"""
    global _qdrant_client, _embedding_model, _vectorstore

    try:
        if _qdrant_client:
            _qdrant_client.close()
            _qdrant_client = None

        _embedding_model = None
        _vectorstore = None

        logger.info("Qdrant connections closed")
    except Exception as e:
        logger.error(f"Error closing Qdrant connections: {e}")
