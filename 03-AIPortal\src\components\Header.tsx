import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import { motion } from "framer-motion";
import Logo from "./shared/Logo";
import { useAuth } from "../context/AuthContext";
import NavigationLink from "./shared/NavigationLink";

const Header = () => {
  const auth = useAuth();
  const theme = useTheme();
  const isBelowMd = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
    >
      <AppBar
        sx={{
          bgcolor: "rgba(0,0,0,0.1)",
          position: "static",
          boxShadow: "none",
          backdropFilter: "blur(10px)",
          borderBottom: "1px solid rgba(255,255,255,0.1)"
        }}
      >
        <Toolbar sx={{
          display: "flex",
          justifyContent: "space-between",
          py: 1,
          px: isBelowMd ? 2 : 4
        }}>
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
          >
            <Logo />
          </motion.div>

          <Box sx={{ display: "flex", gap: 1 }}>
            {auth?.isLoggedIn ? (
              <>
                <NavigationLink
                  bg="linear-gradient(45deg, #4ecdc4, #45b7d1)"
                  to="/chat"
                  text="AI Chat"
                  textColor="white"
                />
                <NavigationLink
                  bg="linear-gradient(45deg, #ff6b6b, #ff5252)"
                  textColor="white"
                  to="/"
                  text="Đăng xuất"
                  onClick={auth.logout}
                />
              </>
            ) : (
              <>
                <NavigationLink
                  bg="linear-gradient(45deg, #4ecdc4, #45b7d1)"
                  to="/login"
                  text="Đăng nhập"
                  textColor="white"
                />
                <NavigationLink
                  bg="linear-gradient(45deg, #ff6b6b, #ff5252)"
                  textColor="white"
                  to="/signup"
                  text="Đăng ký"
                />
              </>
            )}
          </Box>
        </Toolbar>
      </AppBar>
    </motion.div>
  );
};

export default Header;
