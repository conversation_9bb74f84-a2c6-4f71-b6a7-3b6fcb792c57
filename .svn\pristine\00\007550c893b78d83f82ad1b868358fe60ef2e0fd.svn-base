{"version": 3, "file": "no-invalid-void-type.js", "sourceRoot": "", "sources": ["../../src/rules/no-invalid-void-type.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAAqC;AAerC,kBAAe,IAAA,iBAAU,EAAwB;IAC/C,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,yDAAyD;YACtE,WAAW,EAAE,QAAQ;SACtB;QACD,QAAQ,EAAE;YACR,qBAAqB,EACnB,qDAAqD;YACvD,6BAA6B,EAC3B,+DAA+D;YACjE,oBAAoB,EAAE,sCAAsC;YAC5D,+BAA+B,EAC7B,gEAAgE;YAClE,wCAAwC,EACtC,iGAAiG;YACnG,2BAA2B,EACzB,oDAAoD;SACvD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,2BAA2B,EAAE;wBAC3B,KAAK,EAAE;4BACL,EAAE,IAAI,EAAE,SAAS,EAAE;4BACnB;gCACE,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACzB,QAAQ,EAAE,CAAC;6BACZ;yBACF;qBACF;oBACD,oBAAoB,EAAE;wBACpB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd,EAAE,2BAA2B,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE;KACnE;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,2BAA2B,EAAE,oBAAoB,EAAE,CAAC;QACrE,MAAM,YAAY,GAAqB;YACrC,sBAAc,CAAC,gBAAgB,EAAE,EAAE;SACpC,CAAC;QACF,MAAM,mBAAmB,GAAqB;YAC5C,sBAAc,CAAC,mBAAmB;YAClC,sBAAc,CAAC,cAAc;YAC7B,sBAAc,CAAC,kBAAkB;YACjC,sBAAc,CAAC,UAAU;SAC1B,CAAC;QACF,MAAM,iBAAiB,GAAqB;YAC1C,sBAAc,CAAC,aAAa;YAC5B,sBAAc,CAAC,cAAc;SAC9B,CAAC;QAEF,IAAI,2BAA2B,KAAK,IAAI,EAAE,CAAC;YACzC,YAAY,CAAC,IAAI,CAAC,sBAAc,CAAC,4BAA4B,CAAC,CAAC;QACjE,CAAC;QAED;;;;;;WAMG;QACH,SAAS,wBAAwB,CAAC,IAA4B;YAC5D,iCAAiC;YACjC,6BAA6B;YAC7B,0BAA0B;YAC1B,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,4BAA4B;gBAChE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAC1D,CAAC;gBACD,OAAO;YACT,CAAC;YAED,kBAAkB;YAClB,IAAI,KAAK,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAAE,CAAC;gBAC/C,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM,kBAAkB,GAAG,UAAU;qBAClC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;qBACpC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBAEtB,IACE,CAAC,2BAA2B;qBACzB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;qBAC9B,QAAQ,CAAC,kBAAkB,CAAC,EAC/B,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,uBAAuB;wBAClC,IAAI,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE;wBACrC,IAAI;qBACL,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACjC,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,oBAAoB;wBAC7B,CAAC,CAAC,iCAAiC;wBACnC,CAAC,CAAC,sBAAsB;oBAC1B,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED;;WAEG;QACH,SAAS,gBAAgB,CACvB,IAA4B,EAC5B,UAAoC;YAEpC,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBAChC,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,8BAA8B,CAAC,IAAI,CAAC;oBAC/C,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED;;;;WAIG;QACH,SAAS,gBAAgB,CAAC,IAA0B;YAClD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,MAAM,CAAC,EAAE,CACP,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;gBACvC,yEAAyE;gBACzE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBAC7C,MAAM,CAAC,aAAa,EAAE,IAAI;wBACxB,sBAAc,CAAC,4BAA4B;oBAC7C,MAAM,CAAC,aAAa,CAAC,MAAM;yBACxB,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;yBACxB,QAAQ,CAAC,sBAAc,CAAC,aAAa,CAAC,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,aAAa,CAAC,IAA4B;gBACxC,mFAAmF;gBACnF,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,4BAA4B;oBAChE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAC1D,CAAC;oBACD,wBAAwB,CAAC,IAAI,CAAC,CAAC;oBAC/B,OAAO;gBACT,CAAC;gBAED,sHAAsH;gBACtH,IACE,2BAA2B;oBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACnD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,KAAK,sBAAc,CAAC,aAAa,EAC1D,CAAC;oBACD,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;oBACpC,OAAO;gBACT,CAAC;gBAED,wFAAwF;gBACxF,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;oBAC/C,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAC7B,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,mCAAmC;gBACnC,IACE,oBAAoB;oBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACpD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;oBACrD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,EAClC,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,gBAAgB;gBAChB,IACE,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACvC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAO,CAAC,IAAI,CAAC,EACvD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EACP,2BAA2B,IAAI,oBAAoB;wBACjD,CAAC,CAAC,0CAA0C;wBAC5C,CAAC,CAAC,2BAA2B;4BAC3B,CAAC,CAAC,8BAA8B,CAAC,IAAI,CAAC;4BACtC,CAAC,CAAC,oBAAoB;gCACpB,CAAC,CAAC,iCAAiC;gCACnC,CAAC,CAAC,sBAAsB;oBAChC,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,8BAA8B,CACrC,IAA4B;IAE5B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;QACpD,CAAC,CAAC,6BAA6B;QAC/B,CAAC,CAAC,+BAA+B,CAAC;AACtC,CAAC"}